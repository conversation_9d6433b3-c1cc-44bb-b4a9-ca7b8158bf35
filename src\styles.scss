/* You can add global styles to this file, and also import other style files */
@forward 'scss/styles.scss';

/* Import RTL fixes for Arabic language support */
@import 'scss/rtl-fixes';

/* Global RTL Select Fixes - High Priority */
[dir="rtl"] {

  /* Universal select targeting with highest specificity */
  select,
  .form-select,
  [cSelect],
  input[type="select"],
  .custom-select,
  .bootstrap-select {
    text-align: right !important;
    direction: rtl !important;
    background-position: left 0.75rem center !important;
    padding-left: 2.25rem !important;
    padding-right: 0.75rem !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-size: 16px 12px !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
  }

  /* Select options */
  select option,
  .form-select option {
    text-align: right !important;
    direction: rtl !important;
  }

  /* Focus states */
  select:focus,
  .form-select:focus,
  [cSelect]:focus {
    background-position: left 0.75rem center !important;
    text-align: right !important;
    direction: rtl !important;
  }
}

/* Dark mode select fixes */
[dir="rtl"][data-coreui-theme="dark"] {
  select,
  .form-select,
  [cSelect] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;

    &:focus {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
    }
  }
}

/* Additional Angular Material styles if needed */
@use '@angular/material' as mat;

// Add any additional Angular Material theming if needed
// $custom-theme: mat.define-light-theme((
//   color: (
//     primary: $custom-primary,
//     accent: $custom-accent,
//     warn: $custom-warn,
//   )
// ));

// Include any additional global Angular styles here

// Global icon fixes - CRITICAL FIX FOR ICON VISIBILITY
svg[cIcon] {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  fill: currentColor !important;
  stroke: currentColor !important;
  z-index: 10 !important;
  pointer-events: auto !important;
  min-width: 16px !important;
  min-height: 16px !important;
}

// Force all c-icon elements to be visible and properly rendered
c-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 5 !important;
  min-width: 16px !important;
  min-height: 16px !important;

  &::before, &::after {
    display: none !important;
  }
}

// Fix CoreUI icon implementation issues
:root {
  --cui-icon-size-sm: 16px !important;
  --cui-icon-size-md: 20px !important;
  --cui-icon-size-lg: 24px !important;
  --cui-icon-size-xl: 32px !important;
}

// Add your other global styles below

/* RTL Support for Arabic text */
html[lang="ar"] {
  direction: rtl;
  text-align: right;

  .sidebar {
    right: 0;
    left: auto;
  }

  .header {
    padding-right: var(--cui-header-padding-x);
    padding-left: 0;
  }

  .header-toggler {
    margin-left: 0;
    margin-right: auto;
  }

  .wrapper {
    padding-inline: var(--cui-sidebar-occupy-end, 0) var(--cui-sidebar-occupy-start, 0);
  }

  .sidebar-brand {
    margin-right: 0;
    margin-left: auto;
  }

  .nav {
    padding-right: 0;
  }

  // RTL text direction for form elements
  .form-control,
  .form-select {
    text-align: right;
  }
}

// Extend utility classes for RTL layouts
.ms-auto-rtl {
  html[lang="ar"] & {
    margin-right: auto !important;
    margin-left: 0 !important;
  }
}

.me-auto-rtl {
  html[lang="ar"] & {
    margin-left: auto !important;
    margin-right: 0 !important;
  }
}

// Float utilities for RTL
html[lang="ar"] {
  .float-start {
    float: right !important;
  }

  .float-end {
    float: left !important;
  }
}

// Responsive RTL classes
@media (min-width: 768px) {
  html[lang="ar"] {
    .text-md-start {
      text-align: right !important;
    }

    .text-md-end {
      text-align: left !important;
    }
  }
}

/* Leaflet PNG path fix */
.leaflet-default-icon-path {
  background-image: url(assets/leaflet/marker-icon.png);
}

.leaflet-control-layers-toggle {
  background-image: url(assets/leaflet/layers.png);
  width: 36px;
  height: 36px;
}

.leaflet-retina .leaflet-control-layers-toggle {
  background-image: url(assets/leaflet/layers-2x.png);
  background-size: 26px 26px;
}

.leaflet-marker-icon,
.leaflet-marker-shadow {
  background-image: none !important;
}
