import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { selectFcmToken } from '../../store/auth/auth.selectors';
import { saveFcmToken, clearFcmToken } from '../../store/auth/auth.actions';

@Component({
  selector: 'app-fcm-debug',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="fcm-debug-panel">
      <h4>🔔 FCM Token Debug Panel</h4>
      
      <div class="debug-section">
        <h5>Store State:</h5>
        <div class="debug-item">
          <strong>FCM Token from Store:</strong>
          <code>{{ (fcmToken$ | async) || 'null' }}</code>
        </div>
      </div>

      <div class="debug-section">
        <h5>localStorage:</h5>
        <div class="debug-item">
          <strong>fcmToken:</strong>
          <code>{{ localStorageToken || 'null' }}</code>
        </div>
        <div class="debug-item">
          <strong>fcmTokenData:</strong>
          <pre>{{ localStorageTokenData | json }}</pre>
        </div>
      </div>

      <div class="debug-section">
        <h5>Token Info:</h5>
        <div class="debug-item">
          <strong>Has Valid Token:</strong>
          <span [class]="hasValidToken ? 'success' : 'error'">
            {{ hasValidToken ? '✅ Yes' : '❌ No' }}
          </span>
        </div>
        <div class="debug-item">
          <strong>Token Age:</strong>
          <span>{{ tokenAge || 'N/A' }}</span>
        </div>
      </div>

      <div class="debug-actions">
        <button (click)="refreshData()" class="btn btn-primary">🔄 Refresh</button>
        <button (click)="generateMockToken()" class="btn btn-success">🎲 Generate Mock Token</button>
        <button (click)="clearToken()" class="btn btn-danger">🗑️ Clear Token</button>
      </div>

      <div class="debug-section">
        <h5>Recent Logs:</h5>
        <div class="logs-container">
          <div *ngFor="let log of recentLogs" [class]="'log-' + log.type">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .fcm-debug-panel {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
      font-family: monospace;
    }

    .debug-section {
      margin-bottom: 20px;
      padding: 15px;
      background: white;
      border-radius: 4px;
      border: 1px solid #e9ecef;
    }

    .debug-item {
      margin-bottom: 10px;
      display: flex;
      align-items: flex-start;
      gap: 10px;
    }

    .debug-item strong {
      min-width: 150px;
      color: #495057;
    }

    .debug-item code {
      background: #e9ecef;
      padding: 2px 6px;
      border-radius: 3px;
      font-size: 12px;
      word-break: break-all;
    }

    .debug-item pre {
      background: #e9ecef;
      padding: 10px;
      border-radius: 3px;
      font-size: 11px;
      margin: 0;
      max-height: 100px;
      overflow-y: auto;
    }

    .success {
      color: #28a745;
      font-weight: bold;
    }

    .error {
      color: #dc3545;
      font-weight: bold;
    }

    .debug-actions {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
    }

    .btn-primary { background: #007bff; color: white; }
    .btn-success { background: #28a745; color: white; }
    .btn-danger { background: #dc3545; color: white; }

    .logs-container {
      max-height: 200px;
      overflow-y: auto;
      background: #f8f9fa;
      padding: 10px;
      border-radius: 4px;
    }

    .log-info { color: #17a2b8; }
    .log-success { color: #28a745; }
    .log-error { color: #dc3545; }
    .log-warning { color: #ffc107; }

    .log-time {
      font-size: 10px;
      color: #6c757d;
      margin-right: 10px;
    }

    .log-message {
      font-size: 11px;
    }
  `]
})
export class FcmDebugComponent implements OnInit {
  fcmToken$: Observable<string | null>;
  localStorageToken: string | null = null;
  localStorageTokenData: any = null;
  hasValidToken: boolean = false;
  tokenAge: string | null = null;
  recentLogs: Array<{time: string, message: string, type: string}> = [];

  constructor(private store: Store) {
    this.fcmToken$ = this.store.select(selectFcmToken);
  }

  ngOnInit() {
    this.refreshData();
    this.addLog('FCM Debug Panel initialized', 'info');
  }

  refreshData() {
    // Get data from localStorage
    this.localStorageToken = localStorage.getItem('fcmToken');
    
    const tokenDataStr = localStorage.getItem('fcmTokenData');
    if (tokenDataStr) {
      try {
        this.localStorageTokenData = JSON.parse(tokenDataStr);
      } catch {
        this.localStorageTokenData = null;
      }
    } else {
      this.localStorageTokenData = null;
    }

    // Check if token is valid
    this.hasValidToken = this.checkTokenValidity();
    
    // Calculate token age
    this.tokenAge = this.calculateTokenAge();

    this.addLog('Data refreshed', 'info');
  }

  generateMockToken() {
    const mockToken = `fcm_${Date.now()}_${Math.random().toString(36).substring(2, 15)}_debug`;
    this.store.dispatch(saveFcmToken({ fcmToken: mockToken }));
    this.addLog(`Generated mock token: ${mockToken.substring(0, 30)}...`, 'success');
    setTimeout(() => this.refreshData(), 100);
  }

  clearToken() {
    this.store.dispatch(clearFcmToken());
    this.addLog('Token cleared', 'warning');
    setTimeout(() => this.refreshData(), 100);
  }

  private checkTokenValidity(): boolean {
    if (!this.localStorageToken || !this.localStorageTokenData) {
      return false;
    }

    try {
      const tokenAge = Date.now() - new Date(this.localStorageTokenData.timestamp).getTime();
      const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days
      return tokenAge < maxAge;
    } catch {
      return false;
    }
  }

  private calculateTokenAge(): string | null {
    if (!this.localStorageTokenData?.timestamp) {
      return null;
    }

    try {
      const tokenDate = new Date(this.localStorageTokenData.timestamp);
      const now = new Date();
      const diffMs = now.getTime() - tokenDate.getTime();
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

      if (diffDays > 0) {
        return `${diffDays} days, ${diffHours} hours`;
      } else if (diffHours > 0) {
        return `${diffHours} hours, ${diffMinutes} minutes`;
      } else {
        return `${diffMinutes} minutes`;
      }
    } catch {
      return 'Invalid date';
    }
  }

  private addLog(message: string, type: string) {
    const time = new Date().toLocaleTimeString();
    this.recentLogs.unshift({ time, message, type });
    
    // Keep only last 10 logs
    if (this.recentLogs.length > 10) {
      this.recentLogs = this.recentLogs.slice(0, 10);
    }
  }
}
