/* Theme System - Dark/Light Mode Support */

// Base theme variables
:root {
  // Light theme (default)
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f8f9fa;
  --theme-bg-tertiary: #e9ecef;
  --theme-text-primary: #212529;
  --theme-text-secondary: #6c757d;
  --theme-text-muted: #adb5bd;
  --theme-border-color: #dee2e6;
  --theme-shadow: rgba(0, 0, 0, 0.1);
  --theme-shadow-hover: rgba(0, 0, 0, 0.15);

  // Brand colors (consistent across themes)
  --theme-primary: #CD2C4E;
  --theme-primary-hover: #a02240;
  --theme-success: #40C5AA;
  --theme-success-hover: #369688;
  --theme-info: #17a2b8;
  --theme-info-hover: #138496;
  --theme-warning: #ffc107;
  --theme-warning-hover: #e0a800;
  --theme-danger: #dc3545;
  --theme-danger-hover: #c82333;
}

// Dark theme
.dark-theme,
[data-coreui-theme="dark"] {
  --theme-bg-primary: #1a1d23;
  --theme-bg-secondary: #2d3748;
  --theme-bg-tertiary: #374151;
  --theme-text-primary: #ffffff;
  --theme-text-secondary: #e2e8f0;
  --theme-text-muted: #a0aec0;
  --theme-border-color: #4a5568;
  --theme-shadow: rgba(0, 0, 0, 0.3);
  --theme-shadow-hover: rgba(0, 0, 0, 0.4);

  // Override CoreUI variables for dark theme
  --cui-body-bg: var(--theme-bg-primary);
  --cui-body-color: var(--theme-text-primary);
  --cui-border-color: var(--theme-border-color);
  --cui-tertiary-bg: var(--theme-bg-tertiary);
  --cui-secondary-bg: var(--theme-bg-secondary);

  // Card backgrounds
  --cui-card-bg: var(--theme-bg-secondary);
  --cui-card-border-color: var(--theme-border-color);

  // Form controls
  --cui-input-bg: var(--theme-bg-tertiary);
  --cui-input-border-color: var(--theme-border-color);
  --cui-input-color: var(--theme-text-primary);
  --cui-input-placeholder-color: var(--theme-text-muted);

  // Navigation
  --cui-sidebar-bg: var(--theme-bg-primary);
  --cui-sidebar-color: var(--theme-text-secondary);
  --cui-header-bg: var(--theme-bg-secondary);

  // Tables
  --cui-table-bg: var(--theme-bg-primary);
  --cui-table-striped-bg: var(--theme-bg-secondary);
  --cui-table-hover-bg: var(--theme-bg-tertiary);

  // Dropdowns
  --cui-dropdown-bg: var(--theme-bg-secondary);
  --cui-dropdown-border-color: var(--theme-border-color);
  --cui-dropdown-link-color: var(--theme-text-primary);
  --cui-dropdown-link-hover-bg: var(--theme-bg-tertiary);
}

// Light theme explicit
.light-theme,
[data-coreui-theme="light"] {
  --cui-body-bg: var(--theme-bg-primary);
  --cui-body-color: var(--theme-text-primary);
  --cui-border-color: var(--theme-border-color);
  --cui-tertiary-bg: var(--theme-bg-tertiary);
  --cui-secondary-bg: var(--theme-bg-secondary);
}

// Component-specific theme styles
.card {
  background-color: var(--theme-bg-primary);
  border-color: var(--theme-border-color);
  color: var(--theme-text-primary);

  .card-header {
    background-color: var(--theme-bg-secondary);
    border-bottom-color: var(--theme-border-color);
    color: var(--theme-text-primary);

    &.bg-primary {
      background-color: var(--theme-primary) !important;
      color: white !important;
    }

    &.bg-success {
      background-color: var(--theme-success) !important;
      color: white !important;
    }

    &.bg-info {
      background-color: var(--theme-info) !important;
      color: white !important;
    }
  }
}

// Form controls theme
.form-control,
.form-select {
  background-color: var(--cui-input-bg);
  border-color: var(--cui-input-border-color);
  color: var(--cui-input-color);

  &:focus {
    background-color: var(--cui-input-bg);
    border-color: var(--theme-primary);
    box-shadow: 0 0 0 0.2rem rgba(205, 44, 78, 0.25);
  }

  &::placeholder {
    color: var(--cui-input-placeholder-color);
  }
}

// Button theme adjustments
.btn {
  &.btn-primary {
    background-color: var(--theme-primary);
    border-color: var(--theme-primary);

    &:hover, &:focus {
      background-color: var(--theme-primary-hover);
      border-color: var(--theme-primary-hover);
    }
  }

  &.btn-success {
    background-color: var(--theme-success);
    border-color: var(--theme-success);

    &:hover, &:focus {
      background-color: var(--theme-success-hover);
      border-color: var(--theme-success-hover);
    }
  }

  &.btn-outline-primary {
    color: var(--theme-primary);
    border-color: var(--theme-primary);

    &:hover, &:focus {
      background-color: var(--theme-primary);
      border-color: var(--theme-primary);
    }
  }
}

// Table theme
.table {
  --cui-table-color: var(--theme-text-primary);
  --cui-table-bg: var(--theme-bg-primary);
  --cui-table-border-color: var(--theme-border-color);
  --cui-table-striped-bg: var(--theme-bg-secondary);
  --cui-table-hover-bg: var(--theme-bg-tertiary);

  color: var(--theme-text-primary);

  th {
    background-color: var(--theme-bg-secondary);
    color: var(--theme-text-primary);
    border-bottom-color: var(--theme-border-color);
  }
}

// Navigation theme
.sidebar {
  background-color: var(--cui-sidebar-bg);

  .nav-link {
    color: var(--cui-sidebar-color);

    &:hover {
      background-color: var(--theme-bg-tertiary);
    }

    &.active {
      background-color: rgba(205, 44, 78, 0.1);
      color: var(--theme-primary);
    }
  }
}

// Header theme
.header {
  background-color: var(--cui-header-bg);
  border-bottom-color: var(--theme-border-color);
}

// Dropdown theme
.dropdown-menu {
  background-color: var(--cui-dropdown-bg);
  border-color: var(--cui-dropdown-border-color);

  .dropdown-item {
    color: var(--cui-dropdown-link-color);

    &:hover, &:focus {
      background-color: var(--cui-dropdown-link-hover-bg);
    }
  }
}

// Modal theme
.modal-content {
  background-color: var(--theme-bg-primary);
  border-color: var(--theme-border-color);

  .modal-header {
    border-bottom-color: var(--theme-border-color);
  }

  .modal-footer {
    border-top-color: var(--theme-border-color);
  }
}

// Alert theme adjustments
.alert {
  &.alert-primary {
    background-color: rgba(205, 44, 78, 0.1);
    border-color: rgba(205, 44, 78, 0.2);
    color: var(--theme-primary);
  }

  &.alert-success {
    background-color: rgba(64, 197, 170, 0.1);
    border-color: rgba(64, 197, 170, 0.2);
    color: var(--theme-success);
  }
}

// Bootstrap Icons theme adjustments
.bi {
  color: inherit;

  &.text-primary {
    color: var(--theme-primary) !important;
  }

  &.text-success {
    color: var(--theme-success) !important;
  }

  &.text-info {
    color: var(--theme-info) !important;
  }

  &.text-warning {
    color: var(--theme-warning) !important;
  }

  &.text-danger {
    color: var(--theme-danger) !important;
  }
}

// Theme toggle button specific styles
.theme-toggle-btn {
  .bi {
    transition: transform 0.3s ease, color 0.3s ease;
  }

  &:hover .bi {
    transform: scale(1.1);
  }
}

// Icon containers for better theme support
.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  border-radius: 50%;

  .bi {
    font-size: 2rem;
  }
}

// Category and subcategory icons
.category-icon, .subcategory-icon {
  width: 3rem;
  height: 3rem;
  background-color: var(--theme-bg-tertiary);
  border: 2px solid var(--theme-border-color);

  .bi {
    color: var(--theme-primary);
  }
}

.subcategory-icon-img {
  width: 2rem;
  height: 2rem;
  object-fit: cover;
  border-radius: 50%;
}

// Enhanced hover effects for theme compatibility
.btn-hover-effect {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--theme-shadow-hover);
  }
}

.shadow-hover {
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 8px 25px var(--theme-shadow-hover) !important;
  }
}

// Custom scrollbar for dark theme
.dark-theme {
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: var(--theme-bg-secondary);
  }

  ::-webkit-scrollbar-thumb {
    background: var(--theme-border-color);
    border-radius: 4px;

    &:hover {
      background: var(--theme-text-muted);
    }
  }
}

// Smooth transitions for theme changes
* {
  transition: background-color 0.3s ease,
              color 0.3s ease,
              border-color 0.3s ease,
              box-shadow 0.3s ease;
}

// Disable transitions during theme initialization
.theme-transition-disabled * {
  transition: none !important;
}

// Print styles (maintain light theme for printing)
@media print {
  * {
    background-color: white !important;
    color: black !important;
    border-color: #ccc !important;
  }
}
