{"common": {"dashboard": "Dashboard", "settings": "Settings", "profile": "Profile", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "search": "Search", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "loading": "Loading...", "noData": "No Data", "error": "An unexpected error occurred. Please try again.", "success": "Success", "warning": "Warning", "info": "Information", "previous": "Previous", "next": "Next", "first": "First", "last": "Last", "show": "Show", "entries": "Entries", "showing": "Showing", "to": "to", "of": "of", "page": "Page", "firstPage": "First Page", "previousPage": "Previous Page", "nextPage": "Next Page", "lastPage": "Last Page", "firstPageTooltip": "First Page", "previousPageTooltip": "Previous Page", "nextPageTooltip": "Next Page", "lastPageTooltip": "Last Page", "allGovernorates": "All Governorates", "actions": "Actions", "preview": "Preview", "remove": "Remove", "close": "Close", "confirm": "Confirm", "reset": "Reset", "uploadIcon": "Upload Icon", "update": "Update", "add": "Add", "back": "Back", "finish": "Finish", "account": "Account", "toggleSidebar": "Toggle sidebar navigation", "openUserMenu": "Open user menu", "userAvatar": "User avatar", "switchLanguage": "Switch Language", "openThemePicker": "Open theme picker", "messages": {"updates": "Updates", "messages": "Messages", "tasks": "Tasks", "comments": "Comments"}, "notifications": "Notifications", "markAllRead": "<PERSON>", "noNotifications": "No notifications", "viewAll": "View All", "notificationTypes": {"newRequest": "New Request", "requestAccepted": "Request Accepted", "requestRejected": "Request Rejected", "requestCancelled": "Request Cancelled", "requestCompleted": "Request Completed", "newReservation": "New Reservation"}, "clear": "Clear", "refresh": "Refresh", "view": "View", "yes": "Yes", "no": "No", "status": {"cpuUsage": "CPU Usage", "memoryUsage": "Memory Usage", "ssdUsage": "SSD Usage", "processes": "348 Processes. 1/4 Cores.", "memory": "11444GB/16384MB", "storage": "243GB/256GB"}, "tasks": {"upgradeNpm": "Upgrade NPM", "reactVersion": "ReactJS Version", "vueVersion": "VueJS Version", "newLayouts": "Add new layouts", "angularVersion": "Angular Version"}, "time": {"42": "42"}, "colors": "Colors", "typography": "Typography", "components": "Components", "base": "Base", "accordion": "Accordion", "breadcrumbs": "Breadcrumbs", "cards": "Cards", "carousel": "Carousel", "collapse": "Collapse", "listGroup": "List Group", "navsTabs": "Navs & Tabs", "pagination": "Pagination", "placeholder": "Placeholder", "popovers": "Popovers", "progress": "Progress", "spinners": "Spinners", "tables": "Tables", "tabs": "Tabs", "tooltips": "Tooltips", "buttons": "Buttons", "buttonGroups": "Button groups", "dropdowns": "Dropdowns", "forms": "Forms", "formControl": "Form Control", "select": "Select", "checksRadios": "Checks & Radios", "range": "Range", "inputGroup": "Input Group", "floatingLabels": "Floating Labels", "layout": "Layout", "validation": "Validation", "charts": "Charts", "icons": "Icons", "coreuiFree": "CoreUI Free", "coreuiFlags": "CoreUI Flags", "coreuiBrands": "CoreUI Brands", "alerts": "<PERSON><PERSON><PERSON>", "badges": "Badges", "modal": "Modal", "toast": "Toast", "widgets": "Widgets", "extras": "Extras", "pages": "Pages", "error404": "Error 404", "error500": "Error 500", "links": "Links", "docs": "Docs", "confirmDelete": "Are you sure you want to delete this item?"}, "notifications": {"manageAllNotifications": "Manage all your notifications", "unread": "unread", "searchPlaceholder": "Search notifications...", "filterByType": "Filter by Type", "filterByStatus": "Filter by Status", "noNotificationsFound": "No notifications found", "tryDifferentFilters": "Try adjusting your filters or search terms", "noNotificationsYet": "You don't have any notifications yet", "clearFilters": "Clear Filters", "showingResults": "Showing", "pageSize": "Page size", "new": "New", "markRead": "<PERSON> <PERSON>", "read": "Read", "allTypes": "All Types", "allStatus": "All Status"}, "theme": {"light": "Light", "dark": "Dark", "auto": "Auto"}, "auth": {"login": "Sign In", "signIn": "Sign In", "signUp": "Sign Up", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "Enter your phone number", "password": "Password", "passwordPlaceholder": "Enter your password", "forgotPassword": "Forgot Password?", "rememberMe": "Remember Me", "loginError": "Login Failed", "secureLogin": "<PERSON><PERSON>", "secureConnection": "Secure Connection", "dataProtection": "Data Protection", "invalidCredentials": "Phone number or password is wrong", "loginFailed": "<PERSON><PERSON> failed. Please try again.", "signInMessage": "Sign In to your account", "signUpMessage": "Create a new account to get started with our platform. Join us today and experience the benefits of our services.", "registerNow": "Register Now!"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "minLength": "Minimum length is {0} characters", "maxLength": "Maximum length is {0} characters", "egyptPhone": "Please enter a valid Egyptian phone number (e.g., ***********)", "phoneError": "Phone Number Error", "passwordMinLength": "Password must be at least 6 characters long", "phoneRequired": "Phone number is required", "passwordRequired": "Password is required"}, "admin": {"title": "Admins Management", "searchPlaceholder": "Search by name or phone number", "search": "Search", "create": "Create Admin", "name": "Name", "phone": "Phone Number", "image": "Image", "actions": "Actions", "edit": "Edit", "delete": "Delete", "confirmDelete": "Are you sure you want to delete this admin?", "firstName": "First Name", "lastName": "Last Name", "password": "Password", "confirmPassword": "Confirm Password", "imageUpload": "Upload Image", "noImage": "No Image", "noResults": "No admins found", "tryDifferentSearch": "Try different search", "editTooltip": "Edit Admin", "deleteTooltip": "Delete Admin"}, "location": {"title": "Location Management", "governorates": "Governorates", "cities": "Cities", "addGovernorate": "Add Governorate", "addCity": "Add City", "editGovernorate": "Edit Governorate", "editCity": "Edit City", "updateGovernorate": "Update Governorate", "updateCity": "Update City", "governorateName": "Governorate Name", "cityName": "City Name", "selectGovernorate": "Select Governorate", "noGovernorates": "No governorates found", "noCities": "No cities found for this governorate", "loading": "Loading...", "error": "Error loading data", "deleteGovernorateConfirm": "Are you sure you want to delete this governorate?", "deleteCityConfirm": "Are you sure you want to delete this city?", "deleteGovernorateSuccess": "Governorate deleted successfully", "deleteCitySuccess": "City deleted successfully", "deleteGovernorateError": "Error deleting governorate", "deleteCityError": "Error deleting city", "selectGovernorateFirst": "Please select a governorate first", "addCityToStart": "Add New City", "selectGovernorateInstruction": "Select a governorate to add a city", "allGovernorates": "All Governorates"}, "disease": {"title": "Disease Management", "searchPlaceholder": "Search by disease name", "create": "Add Disease", "edit": "Edit Disease", "nameEn": "English Name", "nameAr": "Arabic Name", "nameEnPlaceholder": "Enter disease name in English", "nameArPlaceholder": "Enter disease name in Arabic", "confirmDelete": "Are you sure you want to delete this disease?", "noResults": "No diseases found", "tryDifferentSearch": "Try a different search term", "deleteSuccess": "Disease deleted successfully", "addSuccess": "Disease added successfully", "updateSuccess": "Disease updated successfully"}, "serviceCategory": {"title": "Service Categories Management", "noSubCategoriesDescription": "No sub-categories available", "description": "Manage and organize your service categories effectively", "add": "Add New Category", "edit": "Edit Category", "delete": "Delete Category", "nameAr": "Category Name (Arabic)", "nameEn": "Category Name (English)", "descriptionAr": "Category Description (Arabic)", "descriptionEn": "Category Description (English)", "icon": "Category Icon", "actions": "Available Actions", "confirmDelete": "Are you sure you want to delete this category?", "noData": "No service categories available", "empty": "No Service Categories", "emptyDescription": "Start by adding new service categories to organize your services", "addFirst": "Add First Category", "addSubCategory": "Add Sub-Category", "nameArPlaceholder": "Enter category name in Arabic", "nameEnPlaceholder": "Enter category name in English", "descriptionArPlaceholder": "Enter category description in Arabic", "descriptionEnPlaceholder": "Enter category description in English", "subCategories": "Sub-Categories", "deleteTitle": "Delete Category", "deleteConfirmation": "This category and all its associated sub-categories will be deleted. Are you sure you want to proceed?", "loading": "Loading categories...", "error": "Error loading categories", "success": "Category saved successfully", "errorSaving": "Error saving category", "errorDeleting": "Error deleting category", "successDeleting": "Category deleted successfully", "searchPlaceholder": "Search categories...", "noResults": "No results found", "parentCategory": "Parent Category", "selectParentCategory": "Select Parent Category", "noParentCategory": "No parent category", "iconUpload": "Upload Icon", "iconPreview": "Preview Icon", "iconRemove": "Remove Icon", "iconRequired": "Icon is required", "nameRequired": "Category name is required", "descriptionRequired": "Category description is required", "maxLength": "Maximum length is {0} characters", "minLength": "Minimum length is {0} characters", "invalidIcon": "Invalid icon format", "iconSize": "Icon size must not exceed {0} KB", "iconTypes": "Allowed icon types: {0}", "save": "Save Category", "editSuccess": "Category updated successfully", "addSuccess": "Category added successfully", "deleteSuccess": "Category deleted successfully", "editError": "Error updating category", "addError": "Error adding category", "deleteError": "Error deleting category", "confirmDeleteSubCategory": "Are you sure you want to delete this sub-category?", "deleteSubCategorySuccess": "Sub-category deleted successfully", "deleteSubCategoryError": "Error deleting sub-category", "addSubCategorySuccess": "Sub-category added successfully", "addSubCategoryError": "Error adding sub-category", "editSubCategorySuccess": "Sub-category updated successfully", "editSubCategoryError": "Error updating sub-category", "viewSubCategories": "View", "noSubCategories": "No sub-categories available", "fromCallCenter": "From Call Center"}, "users": {"title": "Users"}, "specialty": {"title": "Specialties Management", "add": "Add Specialty", "edit": "Edit", "delete": "Delete", "nameAr": "Specialty Name (Arabic)", "nameEn": "Specialty Name (English)", "descriptionAr": "Specialty Description (Arabic)", "descriptionEn": "Specialty Description (English)", "actions": "Actions", "confirmDelete": "Are you sure you want to delete this specialty?", "noData": "No specialties available", "addSuccess": "Specialty added successfully", "editSuccess": "Specialty updated successfully", "deleteSuccess": "Specialty deleted successfully", "addError": "Error adding specialty", "editError": "Error updating specialty", "deleteError": "Error deleting specialty", "nameArRequired": "Specialty name in Arabic is required", "nameEnRequired": "Specialty name in English is required"}, "nurse": {"title": "Nurses Management", "image": "Image", "name": "Name", "phone": "Phone", "specialization": "Specialization", "governorate": "Governorate", "city": "City", "license": "License", "rate": "Rate", "actions": "Actions", "edit": "Edit", "delete": "Delete", "noData": "No nurses found.", "confirmDelete": "Are you sure you want to delete this nurse?", "noNurses": "No nurses found.", "addNurse": "Add Nurse", "editNurse": "Edit Nurse", "deleteNurse": "Delete Nurse", "addNurseSuccess": "Nurse added successfully", "editNurseSuccess": "Nurse updated successfully", "deleteNurseSuccess": "Nurse deleted successfully", "addNurseError": "Error adding nurse", "editNurseError": "Error updating nurse", "deleteNurseError": "Error deleting nurse", "updateSuccess": "Nurse updated successfully", "addSuccess": "Nurse added successfully", "deleteSuccess": "Nurse deleted successfully", "location": "Location on Map", "add": "Add Nurse"}, "map": {"instructions": "Click on the map to select a location or drag the marker to adjust position", "showMap": "Show Map", "hideMap": "Hide Map", "latitude": "Latitude", "longitude": "Longitude", "selectLocation": "Select Location", "currentLocation": "Use Current Location", "locationSelected": "Location Selected", "searchLocation": "Search Location"}, "requests": {"title": "Requests Management", "from": "From", "to": "To", "filter": "Filter", "current": "Current", "previous": "Previous", "loading": "Loading...", "total": "Total Requests", "nurse": "Nurse", "phone": "Phone", "status": "Status", "speciality": "Speciality", "totalPrice": "Total Price", "date": "Date", "details": "Details", "detailsTitle": "Request Details", "latitude": "Latitude", "longitude": "Longitude", "nurseLatitude": "Nurse Latitude", "nurseLongitude": "Nurse Longitude", "noRequests": "No requests found.", "prev": "Prev", "next": "Next", "page": "Page", "locationDetails": "Location Details", "distance": "Distance", "distanceKm": "Distance in Kilometers", "distanceBetween": "Distance between {0} and {1}", "distanceToNurse": "Distance to Nurse", "distanceToUser": "Distance to User", "loadingDetails": "Loading details...", "confirmDelete": "Are you sure you want to delete this request?", "delete": "Delete", "edit": "Edit", "view": "View", "cancel": "Cancel", "confirm": "Confirm", "close": "Close"}, "REPORTS": {"TITLE": "Medical Reports", "SUBTITLE": "Manage patient medical reports and health records", "FILTERS": {"FROM_DATE": "From Date", "TO_DATE": "To Date"}, "BUTTONS": {"ADD_REPORT": "Add Report", "SEARCH": "Search", "RESET": "Reset", "VIEW_DETAILS": "View Details", "PATIENT_REPORTS": "Patient Reports"}, "TABLE": {"TITLE": "Reports List", "PATIENT_NAME": "Patient Name", "PATIENT_PHONE": "Patient Phone", "NURSE_NAME": "Nurse Name", "CREATED_AT": "Created Date", "DISEASES": "Diseases", "ACTIONS": "Actions"}, "DETAILS": {"TITLE": "Report Details", "SUBTITLE": "Detailed medical report information", "PATIENT_INFO": "Patient Information", "NURSE_INFO": "Nurse Information", "MEDICAL_INFO": "Medical Information", "DATE_INFO": "Date Information", "PATIENT_NAME": "Patient Name", "PATIENT_PHONE": "Patient Phone", "NURSE_NAME": "Nurse Name", "NURSE_PHONE": "Nurse Phone", "DRUGS": "Medications", "DISEASES": "Diseases", "NOTES": "Notes", "CREATED_DATE": "Created Date", "CREATED_TIME": "Created Time"}, "NO_DATA": {"TITLE": "No Reports Found", "MESSAGE": "No medical reports available. Add a new report to get started."}, "PATIENT_REPORTS": {"TITLE": "Patient Medical Reports", "SUBTITLE": "All medical reports for patient", "FOUND_REPORTS": "Found Reports", "ADD_NEW_REPORT": "Add New Report", "NO_REPORTS": {"TITLE": "No Medical Reports Found", "MESSAGE": "No medical reports have been created for patient", "SUGGESTIONS": {"TITLE": "What you can do:", "ITEM1": "Create a new medical report for this patient", "ITEM2": "Check if the patient has reports under a different name", "ITEM3": "Contact the medical team for more information"}, "ADD_REPORT": "Create First Report"}}}, "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "completed": "Completed", "cancelled": "Cancelled", "approved": "Approved", "rejected": "Rejected"}, "messages": {"success": "Operation completed successfully", "error": "An error occurred", "warning": "Warning", "info": "Information", "deleteSuccess": "Item deleted successfully", "updateSuccess": "Item updated successfully", "addSuccess": "Item added successfully", "deleteError": "Error deleting item", "updateError": "Error updating item", "addError": "Error adding item", "noData": "No data available", "noResults": "No results found"}, "PHARMACY": {"TITLE": "Pharmacies", "ADD_NEW": "Add New Pharmacy", "ADD_TITLE": "Add New Pharmacy", "EDIT_TITLE": "Edit Pharmacy", "TABLE": {"NAME": "Name", "ADDRESS": "Address", "PHONE": "Phone", "EMAIL": "Email", "GOVERNORATE": "Governorate", "CITY": "City", "ACTIONS": "Actions"}, "FORM": {"NAME": "Pharmacy Name", "NAME_PLACEHOLDER": "Enter pharmacy name", "NAME_REQUIRED": "Pharmacy name is required", "ADDRESS": "Address", "ADDRESS_PLACEHOLDER": "Enter pharmacy address", "ADDRESS_REQUIRED": "Address is required", "PHONE": "Phone Number", "PHONE_PLACEHOLDER": "Enter phone number", "PHONE_REQUIRED": "Valid phone number is required", "EMAIL": "Email Address", "EMAIL_PLACEHOLDER": "Enter email address", "EMAIL_INVALID": "Please enter a valid email address", "GOVERNORATE": "Governorate", "GOVERNORATE_PLACEHOLDER": "Select Governorate", "CITY": "City", "CITY_PLACEHOLDER": "Select City", "LOCATION_SECTION": "Location Information", "LATITUDE": "Latitude", "LATITUDE_PLACEHOLDER": "Select location on map", "LONGITUDE": "Longitude", "LONGITUDE_PLACEHOLDER": "Select location on map", "NOTES": "Notes", "NOTES_PLACEHOLDER": "Enter additional notes", "ADDRESS_NOTES": "Address Notes", "ADDRESS_NOTES_PLACEHOLDER": "Enter address details", "VALIDATION_ERROR": "Please fill in all required fields correctly"}, "MAP": {"TITLE": "Select Pharmacy Location", "SELECT_LOCATION": "Select Location on Map", "HIDE_MAP": "Hide Map", "USE_CURRENT_LOCATION": "Use Current Location", "INSTRUCTIONS": "Click on the map to set pharmacy location", "DETAILED_INSTRUCTIONS": "Click anywhere on the map to set the pharmacy location. You can also drag the marker to fine-tune the position.", "LOCATION_ERROR": "Unable to get current location. Please select manually on the map.", "GEOLOCATION_NOT_SUPPORTED": "Geolocation is not supported by this browser."}, "BUTTONS": {"EDIT": "Edit", "DELETE": "Delete", "CANCEL": "Cancel", "ADD": "Add Pharmacy", "UPDATE": "Update Pharmacy"}, "DELETE": {"TITLE": "Delete Pharmacy", "MESSAGE": "Are you sure you want to delete this pharmacy?"}, "MESSAGES": {"ADD_SUCCESS": "Pharmacy added successfully", "UPDATE_SUCCESS": "Pharmacy updated successfully", "DELETE_SUCCESS": "Pharmacy deleted successfully", "DELETE_CONFIRM": "Are you sure you want to delete this pharmacy?", "ERROR": {"LOAD_PHARMACIES": "Error loading pharmacies", "LOAD_GOVERNORATES": "Error loading governorates", "LOAD_CITIES": "Error loading cities", "ADD": "Error adding pharmacy", "UPDATE": "Error updating pharmacy", "DELETE": "Error deleting pharmacy"}}}, "RESERVATION": {"TITLE": "Reservations Management", "SUBTITLE": "Manage patient reservations and appointments", "FILTERS": {"TITLE": "Search & Filter", "SEARCH": "Search", "SEARCH_PLACEHOLDER": "Search by patient name or phone", "STATUS": "Status", "ALL_STATUS": "All Status", "FROM_DATE": "From Date", "TO_DATE": "To Date"}, "TABLE": {"TITLE": "Reservations List", "ID": "ID", "PATIENT_NAME": "Patient Name", "PHONE": "Phone", "DATE": "Date", "TIME": "Time", "STATUS": "Status", "LOCATION": "Location", "ACTIONS": "Actions"}, "STATUS": {"NEW": "New", "IN_PROGRESS": "In Progress", "COMPLETED": "Completed", "CANCELLED": "Cancelled"}, "BUTTONS": {"SEARCH": "Search", "RESET": "Reset", "CLEAR": "Clear", "VIEW_DETAILS": "View Details", "COMPLETE": "Complete", "CANCEL": "Cancel", "DELETE": "Delete", "PRINT": "Print"}, "DETAILS": {"TITLE": "Reservation Details", "SUBTITLE": "Detailed information for reservation", "ID": "Reservation ID", "PATIENT_NAME": "Patient Name", "PHONE": "Phone Number", "DATE": "Appointment Date", "TIME": "Appointment Time", "STATUS": "Status", "GOVERNORATE": "Governorate", "CITY": "City", "ADDRESS_NOTES": "Address Notes", "NOTES": "Notes", "PATIENT_INFO": "Patient Information", "APPOINTMENT_INFO": "Appointment Information", "LOCATION_INFO": "Location Information", "ADDITIONAL_INFO": "Additional Information"}, "CONFIRM": {"TITLE": "Confirm Action", "COMPLETE_TITLE": "Complete Reservation", "COMPLETE_MESSAGE": "Are you sure you want to mark this reservation as completed?", "CANCEL_TITLE": "Cancel Reservation", "CANCEL_MESSAGE": "Are you sure you want to cancel this reservation?", "DELETE_TITLE": "Delete Reservation", "DELETE_MESSAGE": "Are you sure you want to delete this reservation? This action cannot be undone."}, "MESSAGES": {"COMPLETED_SUCCESS": "Reservation completed successfully", "CANCELLED_SUCCESS": "Reservation cancelled successfully", "DELETED_SUCCESS": "Reservation deleted successfully", "LOAD_ERROR": "Error loading reservations", "ACTION_ERROR": "Error performing action"}, "NO_DATA": {"TITLE": "No Reservations Found", "MESSAGE": "No reservations match your current filters. Try adjusting your search criteria."}}}