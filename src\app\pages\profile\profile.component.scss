.profile-page {
  min-height: 100vh;
  padding: 1rem;

  // Profile picture container
  .profile-picture-container {
    position: relative;
    display: inline-block;

    c-avatar {
      border: 3px solid var(--cui-border-color);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    // Hover effect for edit mode
    &:hover {
      c-avatar {
        opacity: 0.8;
        transition: opacity 0.3s ease;
      }
    }
  }

  // Form styling
  .form-control-plaintext {
    padding: 0.375rem 0;
    margin-bottom: 0;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--cui-body-color);
    background-color: transparent;
    border: solid transparent;
    border-width: 1px 0;
  }

  // Card enhancements
  c-card {
    border: 1px solid var(--cui-border-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.3s ease;

    &:hover {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    c-card-header {
      background-color: var(--cui-card-cap-bg);
      border-bottom: 1px solid var(--cui-border-color);

      h5 {
        color: var(--cui-heading-color);
        font-weight: 600;
      }

      i {
        color: var(--cui-primary);
      }
    }
  }

  // Statistics section
  .statistics-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--cui-border-color-translucent);

    &:last-child {
      border-bottom: none;
    }

    .stat-label {
      font-size: 0.875rem;
      color: var(--cui-body-color-secondary);
    }

    .stat-value {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--cui-body-color);
    }
  }

  // Button styling
  button {
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  // Loading state
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
  }

  // Error and success alerts
  c-alert {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    i {
      font-size: 1.1rem;
    }
  }

  // Badge styling
  c-badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
  }

  // Form validation styling
  .is-invalid {
    border-color: var(--cui-form-invalid-border-color);
    box-shadow: 0 0 0 0.2rem rgba(var(--cui-danger-rgb), 0.25);
  }

  .invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: var(--cui-form-invalid-color);
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 0.5rem;

    .profile-picture-container {
      margin-bottom: 1rem;
    }

    .d-flex.justify-content-between {
      flex-direction: column;
      gap: 1rem;

      .d-flex.gap-2 {
        justify-content: center;
      }
    }

    c-card {
      margin-bottom: 1rem;
    }

    .col-md-6 {
      margin-bottom: 1rem;
    }
  }

  @media (max-width: 576px) {
    .row {
      margin: 0;
    }

    .col-lg-8,
    .col-lg-4 {
      padding: 0;
    }

    c-card-body {
      padding: 1rem;
    }

    .profile-picture-container {
      text-align: center;
      margin-bottom: 1.5rem;
    }

    .d-flex.gap-2 {
      flex-direction: column;
      gap: 0.5rem;

      button {
        width: 100%;
      }
    }
  }

  // Dark mode adjustments
  [data-coreui-theme="dark"] & {
    c-card {
      background-color: var(--cui-card-bg);
      border-color: var(--cui-border-color);

      c-card-header {
        background-color: var(--cui-card-cap-bg);
        border-bottom-color: var(--cui-border-color);
      }
    }

    .profile-picture-container c-avatar {
      border-color: var(--cui-border-color);
    }

    .form-control-plaintext {
      color: var(--cui-body-color);
    }
  }

  // Animation for loading states
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  // File input styling
  input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
  }

  // Progress indicators
  .upload-progress {
    margin-top: 0.5rem;

    .progress {
      height: 4px;
      border-radius: 2px;
      background-color: var(--cui-gray-200);

      .progress-bar {
        border-radius: 2px;
        transition: width 0.3s ease;
      }
    }
  }

  // Accessibility improvements
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  // Focus states
  button:focus,
  input:focus,
  select:focus {
    outline: 2px solid var(--cui-primary);
    outline-offset: 2px;
  }

  // RTL specific styles
  :host-context([dir="rtl"]) {
    .d-flex.justify-content-between {
      flex-direction: row-reverse;
    }

    .d-flex.gap-2 {
      flex-direction: row-reverse;
    }

    .profile-picture-container {
      text-align: right;
    }

    .form-control,
    .form-select {
      text-align: right;
      direction: rtl;
    }

    .invalid-feedback {
      text-align: right;
    }

    c-breadcrumb {
      direction: rtl;
    }

    .statistics-item {
      .d-flex.justify-content-between {
        flex-direction: row-reverse;
      }
    }

    // Button groups in RTL
    .d-flex.justify-content-end.gap-2 {
      flex-direction: row-reverse;
      justify-content: flex-start;
    }

    // Modal content RTL
    c-modal-body {
      text-align: right;
      direction: rtl;
    }

    c-modal-footer {
      .d-flex {
        flex-direction: row-reverse;
      }
    }
  }

  // Print styles
  @media print {
    .d-flex.gap-2,
    button,
    c-modal {
      display: none !important;
    }

    c-card {
      break-inside: avoid;
      box-shadow: none !important;
      border: 1px solid #ddd !important;
    }
  }
}
