<div class="request-container">
  <form [formGroup]="filterForm" (ngSubmit)="onFilter()" class="filter-form">
    <label>
      {{ 'requests.from' | translate | async }}:
      <input type="date" formControlName="fromDate">
    </label>
    <label>
      {{ 'requests.to' | translate | async }}:
      <input type="date" formControlName="toDate">
    </label>
    <app-action-button
      type="submit"
      color="primary"
      icon="cilFilter"
      text="requests.filter"
    ></app-action-button>

    <app-action-button
      color="info"
      icon="cilClock"
      text="requests.current"
      [disabled]="!showPrevious"
      (clicked)="toggleRequestType(false)"
    ></app-action-button>

    <app-action-button
      color="secondary"
      icon="cilHistory"
      text="requests.previous"
      [disabled]="showPrevious"
      (clicked)="toggleRequestType(true)"
    ></app-action-button>
  </form>

  <div *ngIf="loading" class="loading">{{ 'requests.loading' | translate | async }}</div>
  <div *ngIf="!loading">
    <div>{{ 'requests.total' | translate | async }}: {{ totalCount }}</div>

    <div class="table-container">
      <div class="table-header">
        <h3>{{ 'requests.title' | translate | async }}</h3>
      </div>
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>#</th>
              <th>{{ 'requests.nurse' | translate | async }}</th>
              <th>{{ 'requests.phone' | translate | async }}</th>
              <th>{{ 'requests.status' | translate | async }}</th>
              <th>{{ 'requests.speciality' | translate | async }}</th>
              <th>{{ 'requests.totalPrice' | translate | async }}</th>
              <th>{{ 'requests.date' | translate | async }}</th>
              <th class="actions-cell">{{ 'requests.details' | translate | async }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let req of requests; let i = index">
              <td>{{ (pageNumber - 1) * pageSize + i + 1 }}</td>
              <td>
                <div class="d-flex align-items-center">
                  <img *ngIf="req.nursePicture" [src]="req.nursePicture" alt="Nurse" class="avatar">
                  <span class="ms-2">{{ req.nurseName }}</span>
                </div>
              </td>
              <td>{{ req.phoneNumber }}</td>
              <td>{{ req.status }}</td>
              <td>{{ req.speciality }}</td>
              <td>{{ req.totalPrice | number:'1.2-2' }}</td>
              <td>{{ req.createdAt | date:'short' }}</td>
              <td class="actions-cell">
                <app-action-button
                  color="primary"
                  size="sm"
                  icon="cilEye"
                  text="requests.details"
                  shape="rounded-pill"
                  (clicked)="showDetails(req.id)"
                ></app-action-button>
              </td>
            </tr>
            <tr *ngIf="requests.length === 0" class="empty-row">
              <td colspan="8">{{ 'requests.noRequests' | translate | async }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Standardized Pagination -->
    <app-pagination
      [currentPage]="pageNumber"
      [totalItems]="totalCount"
      [pageSize]="pageSize"
      [layout]="'simple'"
      [showInfo]="true"
      [size]="'md'"
      (pageChange)="onPageChange($event)"
    ></app-pagination>
  </div>

  <!-- Modal for details -->
  <div class="modal fade show"
       tabindex="-1"
       [ngStyle]="{display: showDetailsModal ? 'block' : 'none', background: 'rgba(0,0,0,0.4)'}"
       style="z-index: 2000; position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; backdrop-filter: blur(4px);"
       *ngIf="showDetailsModal">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">{{ 'requests.requestDetails' | translate | async }}</h5>
          <app-action-button
            color="secondary"
            icon="cilX"
            [iconOnly]="true"
            (clicked)="closeDetails()"
          ></app-action-button>
        </div>
        <div class="modal-body">
          <div *ngIf="loadingDetails" class="text-center my-3">
            <span class="spinner-border"></span> {{ 'requests.loadingDetails' | translate | async }}
          </div>
          <div *ngIf="selectedRequest && !loadingDetails" class="details-container">

            <div class="nurse-info" *ngIf="selectedRequest.nursePicture">
              <img [src]="selectedRequest.nursePicture" alt="Nurse" class="nurse-avatar">
              <div class="nurse-details">
                <div class="nurse-name">{{ selectedRequest.nurseName }}</div>
                <div class="nurse-specialty">{{ selectedRequest.speciality }}</div>
              </div>
            </div>

            <!-- Two column layout for details and map -->
            <div class="details-map-container">
              <!-- Left column: details -->
              <div class="details-column">
                <div class="details-section">
                  <div class="details-row"><strong>{{ 'requests.nurse' | translate | async }}:</strong> {{ selectedRequest.nurseName }}</div>
                  <div class="details-row"><strong>{{ 'requests.phone' | translate | async }}:</strong> {{ selectedRequest.phoneNumber }}</div>
                  <div class="details-row"><strong>{{ 'requests.status' | translate | async }}:</strong> <span class="status-badge">{{ selectedRequest.status }}</span></div>
                  <div class="details-row"><strong>{{ 'requests.speciality' | translate | async }}:</strong> {{ selectedRequest.speciality }}</div>
                  <div class="details-row"><strong>{{ 'requests.totalPrice' | translate | async }}:</strong> <span class="price">{{ selectedRequest.totalPrice | number:'1.2-2' }}</span></div>
                  <div class="details-row"><strong>{{ 'requests.date' | translate | async }}:</strong> {{ selectedRequest.createdAt | date:'full' }}</div>
                </div>
              </div>

              <!-- Right column: map -->
              <div class="map-column">
                <h6>{{ 'requests.locationDetails' | translate | async }}</h6>
                <div id="location-map" class="map-container"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <app-action-button
            color="primary"
            icon="cilX"
            text="common.close"
            (clicked)="closeDetails()"
          ></app-action-button>
        </div>
      </div>
    </div>
  </div>
</div>
