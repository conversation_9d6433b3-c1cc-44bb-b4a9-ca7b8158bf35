// Button icon styling
.btn {
  position: relative;
  overflow: hidden;

  &.d-flex {
    padding: 0.4rem;
  }

  svg {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    fill: currentColor !important;
    stroke: currentColor !important;
  }
}

// Force icon visibility in buttons
button {
  c-icon, svg {
    display: inline-flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 100 !important;
    position: relative !important;
  }
}

// Dropdown styling improvements
.dropdown-item {
  display: flex !important;
  align-items: center !important;

  svg, c-icon {
    display: inline-flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    min-width: 16px !important;
    min-height: 16px !important;
    margin-right: 0.5rem !important;
  }
}

// Nav links with icons
a[cNavLink] {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;

  svg {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}

// Ensure consistent icon display across the app
:host ::ng-deep {
  c-icon {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 16px !important;
    min-height: 16px !important;

    svg {
      display: block !important;
      visibility: visible !important;
      opacity: 1 !important;
      min-width: 16px !important;
      min-height: 16px !important;
    }
  }
}

:host {
  // Notification bell button styling
  .notification-bell-btn {
    &:hover {
      background-color: rgba(0, 0, 0, 0.05) !important;
      border-radius: 0.375rem;
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.25);
      border-radius: 0.375rem;
    }

    // Badge positioning for notification count
    .notification-badge {
      position: absolute;
      top: -4px;
      right: -4px;
      min-width: 18px;
      height: 18px;
      font-size: 0.65rem;
      font-weight: 700;
      line-height: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 4px;
      border: 2px solid var(--cui-body-bg, #fff);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
      z-index: 10;
      background-color: #dc3545 !important;
      color: white !important;

      // Animation for new notifications
      animation: pulse 2s infinite;

      // Responsive sizing
      @media (max-width: 768px) {
        min-width: 16px;
        height: 16px;
        font-size: 0.6rem;
        top: -3px;
        right: -3px;
      }

      // RTL support for Arabic
      [dir="rtl"] & {
        right: auto;
        left: -4px;

        @media (max-width: 768px) {
          left: -3px;
        }
      }
    }
  }

  .notification-dropdown {
    li a {
      padding: 0.75rem 1rem;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);

      &:hover {
        background-color: rgba(0, 0, 0, 0.03);
      }
    }

    .unread-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: var(--cui-info);
    }
  }
}

// Pulse animation for notification badge
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
