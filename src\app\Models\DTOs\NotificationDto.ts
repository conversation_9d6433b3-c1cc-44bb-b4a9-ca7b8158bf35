// Notification DTOs based on the API response structure

export interface NotificationDto {
  id: number;
  title: string;
  body: string;
  createdAt: string;
  type: number;
  requestId: string | null;
  reservationId: string | null;
  isRead: boolean;
  readAt: string | null;
}

export interface NotificationListRequest {
  pageNumber: number;
  pageSize: number;
}

export interface NotificationListResponse {
  items: NotificationDto[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  count: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface NotificationApiResponse {
  status: number;
  message: string;
  internalMessage: string | null;
  data: NotificationListResponse;
  subStatus: number;
}

export interface SingleNotificationApiResponse {
  status: number;
  message: string;
  internalMessage: string | null;
  data: NotificationDto;
  subStatus: number;
}

// Enum for notification types
export enum NotificationType {
  PatientRequest = 1,
  RequestApproval = 2,
  ReservationApproval = 3,
  // Add more types as needed
}

// Helper interface for UI display
export interface NotificationDisplayItem {
  id: number;
  title: string;
  body: string;
  time: Date;
  type: NotificationType;
  isRead: boolean;
  icon: string;
  color: string;
  requestId?: string;
  reservationId?: string;
}
