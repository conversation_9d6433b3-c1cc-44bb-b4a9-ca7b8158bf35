<!--<c-header class="mb-4 d-print-none" position="sticky">-->
<ng-container>
  <c-container [fluid]="true" class="border-bottom px-4">
    <button
      [cSidebarToggle]="sidebarId()"
      cHeaderToggler
      class="btn btn-icon d-flex align-items-center justify-content-center"
      toggle="visible"
      style="margin-inline-start: -14px; width: 40px; height: 40px; position: relative; z-index: 100;"
      [attr.aria-label]="'common.toggleSidebar' | translate | async"
    >
      <svg
        width="20"
        height="20"
        viewBox="0 0 512 512"
        fill="currentColor"
        style="visibility: visible !important; opacity: 1 !important; display: block !important;"
      >
        <rect width="352" height="32" x="80" y="96" rx="16" ry="16"></rect>
        <rect width="352" height="32" x="80" y="240" rx="16" ry="16"></rect>
        <rect width="352" height="32" x="80" y="384" rx="16" ry="16"></rect>
      </svg>
    </button>
    <c-header-nav class="d-none d-md-flex">
      <c-nav-item>
        <a cNavLink routerLink="/dashboard" routerLinkActive="active">{{ 'common.dashboard' | translate | async }}</a>
      </c-nav-item>
      <c-nav-item>
        <a cNavLink routerLink="/users" routerLinkActive="active">{{ 'users.title' | translate | async }}</a>
      </c-nav-item>
      <c-nav-item>
        <a cNavLink routerLink="/settings" routerLinkActive="active">{{ 'common.settings' | translate | async }}</a>
      </c-nav-item>
    </c-header-nav>

    <c-header-nav class="d-none d-md-flex ms-auto">
      <c-dropdown alignment="end" variant="nav-item">
        <button
          [caret]="false"
          cDropdownToggle
          class="position-relative py-0 d-flex align-items-center justify-content-center"
          style="width: 40px; height: 40px;"
          [attr.aria-label]="'common.notifications' | translate | async"
        >
          <i class="bi bi-bell-fill" style="font-size: 1.25rem;"></i>
          <c-badge *ngIf="unreadCount > 0"
                   color="danger"
                   position="top-end"
                   shape="rounded-pill"
                   size="sm">
            {{ unreadCount > 99 ? '99+' : unreadCount }}
          </c-badge>
        </button>
        <ul cDropdownMenu class="pt-0 notification-dropdown" style="width: 320px; max-height: 400px; overflow-y: auto;">
          <li>
            <h6 cDropdownHeader class="bg-body-secondary text-body-secondary fw-semibold py-2 d-flex justify-content-between">
              <span>{{ 'common.notifications' | translate | async }}</span>
              <button *ngIf="unreadCount > 0"
                      class="btn btn-link btn-sm p-0 text-decoration-none"
                      (click)="markAllAsRead()">
                {{ 'common.markAllRead' | translate | async }}
              </button>
            </h6>
          </li>

          <ng-container *ngIf="notifications.length === 0">
            <li class="text-center py-3">
              <span class="text-body-secondary">{{ 'common.noNotifications' | translate | async }}</span>
            </li>
          </ng-container>

          <ng-container *ngFor="let notification of notifications">
            <li>
              <a cDropdownItem
                 [ngClass]="{'bg-light': !notification.read}"
                 (click)="markAsRead(notification)">
                <div class="d-flex align-items-center">
                  <div class="me-3">
                    <i [class]="'bi bi-' + (notification.icon === 'cil-bell' ? 'bell-fill' : 'info-circle-fill')"
                       [ngClass]="'text-' + notification.color"
                       style="font-size: 1.25rem;"></i>
                  </div>
                  <div class="flex-grow-1">
                    <div class="fw-semibold">{{ notification.title | translate | async }}</div>
                    <div class="small text-body-secondary" *ngIf="notification.body">{{ notification.body }}</div>
                    <div class="small text-body-secondary">
                      {{ notification.time | date:'short' }}
                    </div>
                  </div>
                  <div *ngIf="!notification.read" class="ms-2">
                    <c-badge color="info" size="sm" shape="rounded-pill"></c-badge>
                  </div>
                </div>
              </a>
            </li>
          </ng-container>

          <li *ngIf="notifications.length > 0">
            <hr cDropdownDivider />
          </li>
          <li>
            <a cDropdownItem routerLink="/notifications" class="text-center">
              <strong>{{ 'common.viewAll' | translate | async }}</strong>
            </a>
          </li>
        </ul>
      </c-dropdown>

      <a cNavLink class="d-flex align-items-center justify-content-center">
        <i class="bi bi-list-ul" style="font-size: 1.25rem;"></i>
      </a>
      <a cNavLink class="d-flex align-items-center justify-content-center">
        <i class="bi bi-envelope-fill" style="font-size: 1.25rem;"></i>
      </a>
    </c-header-nav>

    <c-header-nav class="ms-auto ms-md-0">
      <div class="nav-item py-1">
        <div class="vr h-100 mx-2 text-body text-opacity-75"></div>
      </div>
      <app-language-switcher></app-language-switcher>
      <div class="nav-item py-1">
        <div class="vr h-100 mx-2 text-body text-opacity-75"></div>
      </div>
      <app-theme-toggle></app-theme-toggle>
      <div class="nav-item py-1">
        <div class="vr h-100 mx-2 text-body text-opacity-75"></div>
      </div>
    </c-header-nav>

    <c-header-nav class="mx-0">
      <ng-container *ngTemplateOutlet="userDropdown" />
    </c-header-nav>

  </c-container>
  <!--</c-header>-->


</ng-container>

<ng-template #userDropdown>
  <c-dropdown [popperOptions]="{ placement: 'bottom-start' }" variant="nav-item">
    <button
      [caret]="false"
      cDropdownToggle
      class="py-0 pe-0"
      [attr.aria-label]="'common.openUserMenu' | translate | async"
    >
      <c-avatar
        shape="rounded-1"
        [size]="'md'"
        src="./assets/images/avatars/8.jpg"
        status="success"
        textColor="primary"
        [attr.alt]="'common.userAvatar' | translate | async"
      />
    </button>
    <ul cDropdownMenu class="pt-0 w-auto">
      <li>
        <h6 cDropdownHeader class="bg-body-secondary text-body-secondary fw-semibold py-2 rounded-top">
          {{ 'common.account' | translate | async }}
        </h6>
      </li>
      @for (message of newMessages; track message.id) {
        <li>
          <a cDropdownItem [routerLink]="message.link">
            <i class="bi bi-bell-fill me-2"></i>
            {{ message.title | translate | async }}
            <c-badge class="ms-2 float-end" [color]="message.status">{{ message.time | translate | async }}</c-badge>
          </a>
        </li>
      }
      <li>
        <h6 cDropdownHeader class="bg-body-secondary text-body-secondary fw-semibold py-2">
          {{ 'common.settings' | translate | async }}
        </h6>
      </li>
      <li>
        <a cDropdownItem routerLink="/profile">
          <i class="bi bi-person-fill me-2"></i>
          {{ 'common.profile' | translate | async }}
        </a>
      </li>
      <li>
        <a cDropdownItem routerLink="/settings">
          <i class="bi bi-gear-fill me-2"></i>
          {{ 'common.settings' | translate | async }}
        </a>
      </li>
      <li>
        <hr cDropdownDivider />
      </li>
      <li>
        <a cDropdownItem (click)="onLogout()">
          <i class="bi bi-box-arrow-right me-2"></i>
          {{ 'common.logout' | translate | async }}
        </a>
      </li>
    </ul>
  </c-dropdown>
</ng-template>

<ng-template #themeDropdown>
  <c-dropdown alignment="end" variant="nav-item">
    <button
      [caret]="false"
      cDropdownToggle
      [attr.aria-label]="'common.openThemePicker' | translate | async"
      class="btn d-flex align-items-center justify-content-center"
      style="width: 40px; height: 40px; position: relative; z-index: 100;"
    >
      <!-- Use direct SVG for theme icon instead of dynamic icons() -->
      <svg
        width="20"
        height="20"
        viewBox="0 0 512 512"
        fill="currentColor"
        style="visibility: visible !important; opacity: 1 !important; display: block !important;"
      >
        <path d="M256 118a22 22 0 01-22-22V48a22 22 0 0144 0v48a22 22 0 01-22 22zM256 486a22 22 0 01-22-22v-48a22 22 0 0144 0v48a22 22 0 01-22 22zM369.14 164.86a22 22 0 01-15.56-37.55l33.94-33.94a22 22 0 0131.11 31.11l-33.94 33.94a21.93 21.93 0 01-15.55 6.44zM108.92 425.08a22 22 0 01-15.55-37.56l33.94-33.94a22 22 0 1131.11 31.11l-33.94 33.94a21.94 21.94 0 01-15.56 6.45zM464 278h-48a22 22 0 010-44h48a22 22 0 010 44zM96 278H48a22 22 0 010-44h48a22 22 0 010 44zM403.08 425.08a21.94 21.94 0 01-15.56-6.45l-33.94-33.94a22 22 0 0131.11-31.11l33.94 33.94a22 22 0 01-15.55 37.56zM142.86 164.86a21.89 21.89 0 01-15.55-6.44l-33.94-33.94a22 22 0 0131.11-31.11l33.94 33.94a22 22 0 01-15.56 37.55zM256 358a102 102 0 11102-102 102.12 102.12 0 01-102 102z" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32"/>
      </svg>
    </button>
    <div cDropdownMenu>
      @for (mode of colorModes; track mode.name) {
        <button
          (click)="colorMode.set(mode.name)"
          [active]="colorMode() === mode.name"
          cDropdownItem
          class="d-flex align-items-center"
        >
          <!-- Mode icons (light, dark, auto) replaced with direct SVG -->
          @if (mode.name === 'light') {
            <svg
              width="16"
              height="16"
              viewBox="0 0 512 512"
              fill="currentColor"
              class="me-2"
              style="visibility: visible !important; opacity: 1 !important; display: block !important;"
            >
              <path d="M256 118a22 22 0 01-22-22V48a22 22 0 0144 0v48a22 22 0 01-22 22zM256 486a22 22 0 01-22-22v-48a22 22 0 0144 0v48a22 22 0 01-22 22zM369.14 164.86a22 22 0 01-15.56-37.55l33.94-33.94a22 22 0 0131.11 31.11l-33.94 33.94a21.93 21.93 0 01-15.55 6.44zM108.92 425.08a22 22 0 01-15.55-37.56l33.94-33.94a22 22 0 1131.11 31.11l-33.94 33.94a21.94 21.94 0 01-15.56 6.45zM464 278h-48a22 22 0 010-44h48a22 22 0 010 44zM96 278H48a22 22 0 010-44h48a22 22 0 010 44zM403.08 425.08a21.94 21.94 0 01-15.56-6.45l-33.94-33.94a22 22 0 0131.11-31.11l33.94 33.94a22 22 0 01-15.55 37.56zM142.86 164.86a21.89 21.89 0 01-15.55-6.44l-33.94-33.94a22 22 0 0131.11-31.11l33.94 33.94a22 22 0 01-15.56 37.55zM256 358a102 102 0 11102-102 102.12 102.12 0 01-102 102z" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32"/>
            </svg>
          } @else if (mode.name === 'dark') {
            <svg
              width="16"
              height="16"
              viewBox="0 0 512 512"
              fill="currentColor"
              class="me-2"
              style="visibility: visible !important; opacity: 1 !important; display: block !important;"
            >
              <path d="M160 136c0-30.62 4.51-61.61 16-88C99.57 81.27 48 159.32 48 248c0 119.29 96.71 216 216 216 88.68 0 166.73-51.57 200-128-26.39 11.49-57.38 16-88 16-119.29 0-216-96.71-216-216z" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32"/>
            </svg>
          } @else {
            <svg
              width="16"
              height="16"
              viewBox="0 0 512 512"
              fill="currentColor"
              class="me-2"
              style="visibility: visible !important; opacity: 1 !important; display: block !important;"
            >
              <path d="M48 256c0 114.69 93.31 208 208 208s208-93.31 208-208S370.69 48 256 48 48 141.31 48 256zm128 0a80 80 0 1080-80 80.09 80.09 0 00-80 80z" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32"/>
            </svg>
          }
          {{ 'theme.' + mode.name | translate | async }}
        </button>
      }
    </div>
  </c-dropdown>
</ng-template>
