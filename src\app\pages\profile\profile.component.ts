import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

// CoreUI imports
import {
  CardModule,
  ButtonModule,
  FormModule,
  SpinnerModule,
  BreadcrumbModule,
  AlertModule,
  ModalModule,
  AvatarModule,
  BadgeModule,
  ProgressModule
} from '@coreui/angular';

// Services and models
import { AdminService } from '../../services/admin.service';
import { TranslationService } from '../../services/translation.service';
import { AuthService } from '../../services/auth.service';
import { TranslatePipe } from '../../pipes/translate.pipe';

interface UserProfile {
  id: number;
  name: string;
  email: string;
  phone?: string;
  role: string;
  profilePicture?: string;
  createdAt?: string;
  lastLogin?: string;
  isActive: boolean;
  // Add other relevant user fields
}

interface AdminsDto {
  id: string;
  phoneNumber: string;
  firstName: string;
  lastName: string;
  imageUrl: string;
}

interface UserStats {
  totalRequests?: number;
  completedRequests?: number;
  pendingRequests?: number;
  totalReservations?: number;
  // Add other relevant statistics
}

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    CardModule,
    ButtonModule,
    FormModule,
    SpinnerModule,
    BreadcrumbModule,
    AlertModule,
    ModalModule,
    AvatarModule,
    BadgeModule,
    ProgressModule,
    TranslatePipe
  ],
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss']
})
export class ProfileComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private platformId = inject(PLATFORM_ID);

  // Form and data properties
  profileForm!: FormGroup;
  userProfile: UserProfile | null = null;
  userStats: UserStats = {};
  originalProfile: UserProfile | null = null;

  // UI state properties
  loading = false;
  saving = false;
  editMode = false;
  error: string | null = null;
  successMessage: string | null = null;

  // File upload properties
  selectedFile: File | null = null;
  imagePreview: string | null = null;
  uploadingImage = false;

  // Modal properties
  showCancelModal = false;
  showSaveModal = false;

  // Breadcrumb items
  breadcrumbItems = [
    { label: 'common.dashboard', url: '/dashboard' },
    { label: 'common.profile', active: true }
  ];

  constructor(
    private fb: FormBuilder,
    private adminService: AdminService,
    private authService: AuthService,
    private translationService: TranslationService,
    private router: Router
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.loadUserProfile();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    this.profileForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.pattern(/^[+]?[0-9\s\-\(\)]{10,15}$/)]],
      // Add other editable fields as needed
    });

    // Disable form initially
    this.profileForm.disable();
  }

  private loadUserProfile(): void {
    this.loading = true;
    this.error = null;

    // Get current user ID from auth service
    const currentUser = this.authService.getCurrentUser();
    if (!currentUser || !currentUser.id) {
      this.error = 'User not authenticated';
      this.loading = false;
      this.router.navigate(['/login']);
      return;
    }

    // Load user profile data
    this.adminService.getAdminById(currentUser.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.status === 0 && response.data) {
            // Convert AdminsDto to UserProfile
            const adminData = response.data;
            this.userProfile = {
              id: parseInt(adminData.id),
              name: `${adminData.firstName} ${adminData.lastName}`.trim(),
              email: adminData.phoneNumber, // Assuming phone is used as email in this system
              phone: adminData.phoneNumber,
              role: 'admin', // Default role
              profilePicture: adminData.imageUrl,
              isActive: true,
              createdAt: new Date().toISOString(),
              lastLogin: new Date().toISOString()
            };
            this.originalProfile = { ...this.userProfile };
            this.populateForm();
            this.loadUserStats();
          } else {
            this.error = response.message || 'Failed to load profile';
          }
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading profile:', error);
          this.error = 'Failed to load profile. Please try again.';
          this.loading = false;
        }
      });
  }

  private populateForm(): void {
    if (this.userProfile) {
      this.profileForm.patchValue({
        name: this.userProfile.name,
        email: this.userProfile.email,
        phone: this.userProfile.phone || ''
      });
    }
  }

  private loadUserStats(): void {
    // Load user statistics if available
    // This would depend on your backend API structure
    // For now, we'll set some placeholder data
    this.userStats = {
      totalRequests: 0,
      completedRequests: 0,
      pendingRequests: 0,
      totalReservations: 0
    };
  }

  enableEditMode(): void {
    this.editMode = true;
    this.profileForm.enable();
    this.error = null;
    this.successMessage = null;
  }

  cancelEdit(): void {
    if (this.hasUnsavedChanges()) {
      this.showCancelModal = true;
    } else {
      this.exitEditMode();
    }
  }

  confirmCancel(): void {
    this.showCancelModal = false;
    this.exitEditMode();
  }

  private exitEditMode(): void {
    this.editMode = false;
    this.profileForm.disable();
    this.populateForm(); // Reset form to original values
    this.selectedFile = null;
    this.imagePreview = null;
    this.error = null;
    this.successMessage = null;
  }

  saveProfile(): void {
    if (this.profileForm.valid) {
      this.showSaveModal = true;
    } else {
      this.markFormGroupTouched();
    }
  }

  confirmSave(): void {
    this.showSaveModal = false;
    this.performSave();
  }

  private performSave(): void {
    if (!this.userProfile || !this.profileForm.valid) return;

    this.saving = true;
    this.error = null;

    const formData = this.profileForm.value;
    const updateData = {
      id: this.userProfile.id,
      name: formData.name,
      email: formData.email,
      phone: formData.phone
    };

    this.adminService.updateAdmin(updateData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.status === 0) {
            this.userProfile = { ...this.userProfile!, ...updateData };
            this.originalProfile = { ...this.userProfile };
            this.successMessage = 'Profile updated successfully';
            this.exitEditMode();

            // Upload profile picture if selected
            if (this.selectedFile) {
              this.uploadProfilePicture();
            }
          } else {
            this.error = response.message || 'Failed to update profile';
          }
          this.saving = false;
        },
        error: (error) => {
          console.error('Error updating profile:', error);
          this.error = 'Failed to update profile. Please try again.';
          this.saving = false;
        }
      });
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files[0]) {
      const file = input.files[0];

      // Validate file type
      if (!file.type.startsWith('image/')) {
        this.error = 'Please select a valid image file';
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        this.error = 'File size must be less than 5MB';
        return;
      }

      this.selectedFile = file;

      // Create image preview
      if (isPlatformBrowser(this.platformId)) {
        const reader = new FileReader();
        reader.onload = (e) => {
          this.imagePreview = e.target?.result as string;
        };
        reader.readAsDataURL(file);
      }
    }
  }

  private uploadProfilePicture(): void {
    if (!this.selectedFile || !this.userProfile) return;

    this.uploadingImage = true;

    // Create FormData for file upload
    const formData = new FormData();
    formData.append('profilePicture', this.selectedFile);
    formData.append('userId', this.userProfile.id.toString());

    // Note: You'll need to implement the uploadProfilePicture method in AdminService
    // this.adminService.uploadProfilePicture(formData)
    //   .pipe(takeUntil(this.destroy$))
    //   .subscribe({
    //     next: (response) => {
    //       if (response.status === 0 && response.data) {
    //         this.userProfile!.profilePicture = response.data.profilePictureUrl;
    //         this.selectedFile = null;
    //         this.imagePreview = null;
    //       }
    //       this.uploadingImage = false;
    //     },
    //     error: (error) => {
    //       console.error('Error uploading profile picture:', error);
    //       this.error = 'Failed to upload profile picture';
    //       this.uploadingImage = false;
    //     }
    //   });

    // For now, simulate upload
    setTimeout(() => {
      this.uploadingImage = false;
      this.selectedFile = null;
      this.imagePreview = null;
    }, 2000);
  }

  private hasUnsavedChanges(): boolean {
    if (!this.originalProfile) return false;

    const formData = this.profileForm.value;
    return (
      formData.name !== this.originalProfile.name ||
      formData.email !== this.originalProfile.email ||
      formData.phone !== (this.originalProfile.phone || '') ||
      this.selectedFile !== null
    );
  }

  private markFormGroupTouched(): void {
    Object.keys(this.profileForm.controls).forEach(key => {
      const control = this.profileForm.get(key);
      control?.markAsTouched();
    });
  }

  getFieldError(fieldName: string): string | null {
    const control = this.profileForm.get(fieldName);
    if (control && control.errors && control.touched) {
      if (control.errors['required']) return `${fieldName} is required`;
      if (control.errors['email']) return 'Please enter a valid email address';
      if (control.errors['minlength']) return `${fieldName} is too short`;
      if (control.errors['maxlength']) return `${fieldName} is too long`;
      if (control.errors['pattern']) return `Please enter a valid ${fieldName}`;
    }
    return null;
  }

  getRoleDisplayName(role: string): string {
    // Map role values to display names
    const roleMap: { [key: string]: string } = {
      'admin': 'Administrator',
      'manager': 'Manager',
      'user': 'User',
      'moderator': 'Moderator'
    };
    return roleMap[role] || role;
  }

  getRoleBadgeColor(role: string): string {
    const colorMap: { [key: string]: string } = {
      'admin': 'danger',
      'manager': 'warning',
      'user': 'primary',
      'moderator': 'info'
    };
    return colorMap[role] || 'secondary';
  }

  dismissError(): void {
    this.error = null;
  }

  dismissSuccess(): void {
    this.successMessage = null;
  }
}
