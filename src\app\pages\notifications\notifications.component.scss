:host {
  .notification-card {
    transition: all 0.2s ease;
    cursor: pointer;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    // Unread notification styling
    &.border-start {
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background: linear-gradient(180deg, var(--cui-info) 0%, var(--cui-info-dark, var(--cui-info)) 100%);
        border-radius: 0 2px 2px 0;
      }
    }

    // Read notification styling
    &:not(.border-start) {
      opacity: 0.8;
      
      &:hover {
        opacity: 1;
      }
    }
  }

  .notification-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: rgba(var(--cui-primary-rgb), 0.1);
    font-size: 1.25rem;
    
    // Different background colors based on notification type
    &.type-success {
      background: rgba(var(--cui-success-rgb), 0.1);
    }
    
    &.type-danger {
      background: rgba(var(--cui-danger-rgb), 0.1);
    }
    
    &.type-warning {
      background: rgba(var(--cui-warning-rgb), 0.1);
    }
    
    &.type-info {
      background: rgba(var(--cui-info-rgb), 0.1);
    }
  }

  .notification-body {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.4;
    max-height: 2.8em;
  }

  // Filter section styling
  .filters-section {
    background: var(--cui-body-bg);
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  // Search input styling
  input[cFormControl] {
    &:focus {
      border-color: var(--cui-primary);
      box-shadow: 0 0 0 0.2rem rgba(var(--cui-primary-rgb), 0.25);
    }
  }

  // Select styling
  select[cSelect] {
    &:focus {
      border-color: var(--cui-primary);
      box-shadow: 0 0 0 0.2rem rgba(var(--cui-primary-rgb), 0.25);
    }
  }

  // Empty state styling
  .empty-state {
    padding: 3rem 1rem;
    text-align: center;
    
    i {
      opacity: 0.5;
      margin-bottom: 1rem;
    }
  }

  // Loading state styling
  .loading-state {
    padding: 3rem 1rem;
    text-align: center;
    
    .spinner-border {
      width: 3rem;
      height: 3rem;
      border-width: 0.3em;
    }
  }

  // Results summary styling
  .results-summary {
    font-size: 0.875rem;
    color: var(--cui-secondary);
    margin-bottom: 1rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--cui-border-color);
  }

  // Badge styling
  c-badge {
    &.badge-new {
      animation: pulse 2s infinite;
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .notification-card {
      margin-bottom: 1rem;
      
      c-card-body {
        padding: 1rem;
      }
      
      .notification-icon {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
      }
      
      .d-flex.justify-content-between {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 0.5rem;
        
        .d-flex.gap-2 {
          align-self: flex-end;
        }
      }
    }

    // Stack filters vertically on mobile
    .filters-section {
      .row.g-3 {
        .col-md-2,
        .col-md-3,
        .col-md-4 {
          margin-bottom: 1rem;
        }
      }
    }

    // Adjust header on mobile
    .page-header {
      flex-direction: column;
      align-items: flex-start !important;
      gap: 1rem;
      
      .d-flex.gap-2 {
        width: 100%;
        justify-content: space-between;
      }
    }
  }

  @media (max-width: 576px) {
    .notification-card {
      .notification-body {
        -webkit-line-clamp: 3;
        max-height: 4.2em;
      }
      
      .d-flex.align-items-center.gap-2 {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 0.25rem !important;
      }
    }

    // Single column layout for very small screens
    .filters-section {
      .row.g-3 > div {
        margin-bottom: 1rem;
      }
    }
  }

  // Dark mode adjustments
  [data-coreui-theme="dark"] & {
    .notification-card {
      &.bg-light {
        background-color: rgba(255, 255, 255, 0.05) !important;
      }
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      }
    }

    .notification-icon {
      background: rgba(255, 255, 255, 0.1);
    }

    .filters-section {
      background: var(--cui-dark);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }
  }

  // Animation for new notifications
  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
    100% {
      transform: scale(1);
    }
  }

  // Smooth transitions
  * {
    transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
  }

  // Focus states for accessibility
  button:focus,
  input:focus,
  select:focus {
    outline: 2px solid var(--cui-primary);
    outline-offset: 2px;
  }

  // Print styles
  @media print {
    .notification-card {
      break-inside: avoid;
      box-shadow: none !important;
      border: 1px solid #ddd !important;
    }
    
    .filters-section,
    c-pagination,
    button {
      display: none !important;
    }
  }
}
