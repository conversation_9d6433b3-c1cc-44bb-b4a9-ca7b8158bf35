/* RTL (Right-to-Left) Fixes for Arabic Language Support */

/* ===== SELECT DROPDOWN RTL FIXES ===== */

/* Base RTL direction for Arabic */
[dir="rtl"] {

  /* Select dropdown styling */
  select,
  .form-select,
  [cSelect] {
    /* Fix text alignment */
    text-align: right;
    direction: rtl;

    /* Fix background arrow position */
    background-position: left 0.75rem center !important;
    padding-left: 2.25rem !important;
    padding-right: 0.75rem !important;

    /* Custom arrow for RTL */
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
  }

  /* Select options alignment */
  select option,
  .form-select option {
    text-align: right;
    direction: rtl;
  }

  /* CoreUI specific select fixes */
  .form-select {
    &:focus {
      background-position: left 0.75rem center !important;
    }

    &[multiple] {
      background-image: none !important;
      padding-left: 0.75rem !important;
    }

    &[size]:not([size="1"]) {
      background-image: none !important;
      padding-left: 0.75rem !important;
    }
  }

  /* Input group select fixes */
  .input-group .form-select {
    &:not(:first-child) {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      border-top-left-radius: var(--cui-border-radius);
      border-bottom-left-radius: var(--cui-border-radius);
    }

    &:not(:last-child) {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      border-top-right-radius: var(--cui-border-radius);
      border-bottom-right-radius: var(--cui-border-radius);
    }
  }

  /* Dropdown menu positioning */
  .dropdown-menu {
    text-align: right;
    direction: rtl;

    /* Fix dropdown item alignment */
    .dropdown-item {
      text-align: right;
      padding-left: 1.5rem;
      padding-right: 1rem;

      /* Icon positioning in dropdown items */
      i, svg {
        margin-left: 0.5rem;
        margin-right: 0;
        float: right;
      }
    }

    /* Dropdown header alignment */
    .dropdown-header {
      text-align: right;
      padding-left: 1.5rem;
      padding-right: 1rem;
    }

    /* Dropdown divider */
    .dropdown-divider {
      margin: 0.5rem 1rem 0.5rem 1.5rem;
    }
  }

  /* CoreUI dropdown specific fixes */
  c-dropdown {
    .dropdown-menu {
      text-align: right;

      [cDropdownItem] {
        text-align: right;
        direction: rtl;

        /* Fix icon positioning */
        i {
          margin-left: 0.5rem;
          margin-right: 0;
        }
      }
    }
  }

  /* Notification dropdown specific fixes */
  .notification-dropdown {
    .dropdown-menu {
      min-width: 320px;
      max-width: 400px;

      .notification-item {
        text-align: right;
        direction: rtl;

        .d-flex {
          flex-direction: row-reverse;

          .me-3 {
            margin-left: 1rem !important;
            margin-right: 0 !important;
          }

          .ms-2 {
            margin-right: 0.5rem !important;
            margin-left: 0 !important;
          }
        }
      }
    }
  }

  /* Form control fixes */
  .form-control {
    text-align: right;
    direction: rtl;
  }

  /* Input group fixes */
  .input-group {
    flex-direction: row-reverse;

    .input-group-text {
      &:first-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        border-top-right-radius: var(--cui-border-radius);
        border-bottom-right-radius: var(--cui-border-radius);
      }

      &:last-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-top-left-radius: var(--cui-border-radius);
        border-bottom-left-radius: var(--cui-border-radius);
      }
    }

    .form-control {
      &:not(:first-child) {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }

      &:not(:last-child) {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
    }
  }

  /* Button group fixes */
  .btn-group {
    flex-direction: row-reverse;

    .btn {
      &:first-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        border-top-right-radius: var(--cui-border-radius);
        border-bottom-right-radius: var(--cui-border-radius);
      }

      &:last-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-top-left-radius: var(--cui-border-radius);
        border-bottom-left-radius: var(--cui-border-radius);
      }
    }
  }

  /* Pagination fixes */
  .pagination {
    flex-direction: row-reverse;

    .page-link {
      &:first-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        border-top-right-radius: var(--cui-border-radius);
        border-bottom-right-radius: var(--cui-border-radius);
      }

      &:last-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-top-left-radius: var(--cui-border-radius);
        border-bottom-left-radius: var(--cui-border-radius);
      }
    }
  }

  /* Badge positioning fixes */
  .badge {
    &.ms-auto {
      margin-left: 0 !important;
      margin-right: auto !important;
    }

    &.me-auto {
      margin-right: 0 !important;
      margin-left: auto !important;
    }
  }

  /* Icon positioning fixes */
  .bi {
    &.me-1, &.me-2, &.me-3 {
      margin-left: 0.25rem;
      margin-right: 0;
    }

    &.ms-1, &.ms-2, &.ms-3 {
      margin-right: 0.25rem;
      margin-left: 0;
    }
  }

  /* Table fixes */
  .table {
    text-align: right;

    th, td {
      text-align: right;
    }

    .table-responsive {
      direction: rtl;
    }
  }

  /* Card fixes */
  .card {
    .card-header {
      text-align: right;
    }

    .card-body {
      text-align: right;
    }

    .card-footer {
      text-align: right;
    }
  }

  /* Breadcrumb fixes */
  .breadcrumb {
    flex-direction: row-reverse;

    .breadcrumb-item {
      &::before {
        content: "\\";
        transform: scaleX(-1);
      }

      &:first-child::before {
        content: none;
      }
    }
  }

  /* Modal fixes */
  .modal {
    .modal-header {
      text-align: right;

      .btn-close {
        margin-left: 0;
        margin-right: auto;
      }
    }

    .modal-body {
      text-align: right;
    }

    .modal-footer {
      text-align: right;
      justify-content: flex-start;

      .btn {
        margin-left: 0;
        margin-right: 0.5rem;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  /* Sidebar fixes */
  .sidebar {
    .sidebar-nav {
      text-align: right;

      .nav-link {
        text-align: right;

        .nav-icon {
          margin-left: 0.5rem;
          margin-right: 0;
          float: right;
        }
      }
    }
  }

  /* Navbar fixes */
  .navbar {
    .navbar-nav {
      flex-direction: row-reverse;

      .nav-link {
        text-align: right;
      }
    }

    .navbar-brand {
      margin-left: 0;
      margin-right: 1rem;
    }
  }
}

/* Dark mode RTL fixes */
[dir="rtl"][data-coreui-theme="dark"] {
  select,
  .form-select,
  [cSelect] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") !important;
  }
}

/* Specific component fixes */
[dir="rtl"] {

  /* Notifications page specific fixes */
  .notifications-page {
    .notification-card {
      .d-flex {
        flex-direction: row-reverse;

        .me-3 {
          margin-left: 1rem !important;
          margin-right: 0 !important;
        }

        .ms-2 {
          margin-right: 0.5rem !important;
          margin-left: 0 !important;
        }
      }

      .notification-body {
        text-align: right;
      }

      .d-flex.justify-content-between {
        flex-direction: row-reverse;

        .d-flex.gap-2 {
          flex-direction: row-reverse;
        }
      }
    }
  }

  /* Admin management page fixes */
  .admin-management {
    .table {
      th, td {
        text-align: right;

        &:first-child {
          text-align: center; // For checkboxes/actions
        }
      }
    }

    .btn-group {
      flex-direction: row-reverse;
    }
  }

  /* Pharmacy management page fixes */
  .pharmacy-management {
    .form-row {
      .col {
        text-align: right;
      }
    }

    .map-container {
      direction: ltr; // Keep map controls in LTR
    }
  }

  /* Search and filter components */
  .search-filters {
    .row {
      flex-direction: row-reverse;

      .col {
        text-align: right;
      }
    }

    .btn-group {
      flex-direction: row-reverse;
    }
  }

  /* CoreUI specific component fixes */
  c-card {
    .card-header {
      text-align: right;

      .d-flex {
        flex-direction: row-reverse;

        .ms-auto {
          margin-left: 0 !important;
          margin-right: auto !important;
        }
      }
    }
  }

  c-modal {
    .modal-header {
      flex-direction: row-reverse;

      .btn-close {
        margin-left: 0;
        margin-right: auto;
      }
    }

    .modal-footer {
      flex-direction: row-reverse;

      .btn {
        margin-left: 0;
        margin-right: 0.5rem;

        &:first-child {
          margin-right: 0;
        }
      }
    }
  }

  /* Alert component fixes */
  c-alert {
    text-align: right;

    .alert-dismissible {
      .btn-close {
        left: 0;
        right: auto;
      }
    }
  }

  /* Progress bar fixes */
  .progress {
    direction: ltr; // Keep progress direction LTR for consistency
  }

  /* Badge fixes */
  .badge {
    &.float-end {
      float: left !important;
    }

    &.float-start {
      float: right !important;
    }
  }
}

/* Responsive RTL fixes */
@media (max-width: 768px) {
  [dir="rtl"] {
    .dropdown-menu {
      min-width: 280px;

      .notification-item {
        .d-flex {
          flex-direction: column;
          align-items: flex-end;

          .me-3 {
            margin: 0 0 0.5rem 0 !important;
          }
        }
      }
    }

    .input-group {
      flex-direction: column;

      .form-control,
      .input-group-text {
        border-radius: var(--cui-border-radius) !important;
        margin-bottom: 0.5rem;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .btn-group {
      flex-direction: column;

      .btn {
        border-radius: var(--cui-border-radius) !important;
        margin-bottom: 0.25rem;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .table-responsive {
      .table {
        font-size: 0.875rem;

        th, td {
          padding: 0.5rem 0.25rem;
          white-space: nowrap;
        }
      }
    }
  }
}
